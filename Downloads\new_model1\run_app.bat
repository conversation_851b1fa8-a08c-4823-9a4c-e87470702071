@echo off
echo 🤖 Fine-tuned Llama Model - Streamlit Interface
echo ===============================================

REM Check if model files exist
if not exist "adapter_config.json" (
    echo ❌ Error: adapter_config.json not found!
    echo Please run this script from the directory containing your fine-tuned model files.
    pause
    exit /b 1
)

echo ✅ Model files found!

REM Create virtual environment if it doesn't exist
if not exist "model_env" (
    echo Creating virtual environment...
    python -m venv model_env
    if errorlevel 1 (
        echo ❌ Error creating virtual environment
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created!
) else (
    echo ✅ Virtual environment already exists!
)

REM Install requirements
echo Installing required packages...
call model_env\Scripts\activate.bat && pip install "numpy<2" torch transformers peft streamlit accelerate safetensors sentencepiece protobuf
if errorlevel 1 (
    echo ❌ Error installing requirements
    pause
    exit /b 1
)

echo ✅ Requirements installed successfully!
echo.
echo 🚀 Starting the web interface...
echo The app will open in your browser at: http://localhost:8501
echo Press Ctrl+C to stop the app
echo -----------------------------------------------

REM Run the Streamlit app
call model_env\Scripts\activate.bat && streamlit run app.py --server.port 8501

echo.
echo 👋 App stopped
pause
