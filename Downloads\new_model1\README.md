# Fine-tuned Llama Model - Streamlit Interface

This directory contains a fine-tuned Llama-3.2-1B-Instruct model with LoRA adapters and a Streamlit web interface for local inference.

## Model Details
- **Base Model**: meta-llama/Llama-3.2-1B-Instruct
- **Fine-tuning Method**: LoRA (Low-Rank Adaptation)
- **LoRA Rank**: 64
- **LoRA Alpha**: 16
- **Target Modules**: q_proj, v_proj

## Quick Start

### 🔑 Step 1: Setup Hugging Face Access (Required)

The Llama model requires authentication. You need:
1. A [Hugging Face account](https://huggingface.co/)
2. Access to [Llama-3.2-1B-Instruct](https://huggingface.co/meta-llama/Llama-3.2-1B-Instruct) (request access from Meta)
3. A [Hugging Face access token](https://huggingface.co/settings/tokens)

**Easy Token Setup:**
```bash
python setup_token.py
```

**Manual Token Setup:**
- Create a `.env` file with: `HF_TOKEN=your_token_here`
- Or enter your token in the app's sidebar

### 🚀 Step 2: Run the Application

**Option 1: Using the Batch File (Windows - Easiest)**
```cmd
run_app.bat
```

**Option 2: Using the Python Setup Script**
```bash
python run_app.py
```

**Option 3: Using Virtual Environment (Advanced users)**
```bash
# Create virtual environment
python -m venv model_env

# Activate virtual environment (Windows)
model_env\Scripts\activate

# Install dependencies
pip install "numpy<2" torch transformers peft streamlit accelerate safetensors sentencepiece protobuf

# Run the app
streamlit run app.py --server.port 8501
```

All methods will automatically:
1. Create a virtual environment (if needed)
2. Install all required dependencies with compatible versions
3. Start the Streamlit web interface
4. Open your browser to http://localhost:8501

## Features

- **Interactive Chat Interface**: Clean, modern chat UI powered by Streamlit
- **Real-time Generation**: Stream responses as they're generated
- **Customizable Parameters**: Adjust temperature, top-p, and max length
- **Chat History**: Maintains conversation context
- **GPU Support**: Automatically uses CUDA if available
- **Model Info**: Displays current model and device information

## Usage

1. Start the application using one of the methods above
2. Open your browser to http://localhost:8501
3. Type your message in the chat input at the bottom
4. Adjust generation parameters in the sidebar if needed
5. Use "Clear Chat" to start a new conversation

## System Requirements

- **Python**: 3.8 or higher
- **RAM**: At least 4GB (8GB+ recommended)
- **GPU**: Optional but recommended (CUDA-compatible)
- **Storage**: ~3GB for model files

## Troubleshooting

### Common Issues:

1. **Import Errors / NumPy Compatibility**:
   - Use the provided virtual environment setup
   - The app automatically handles NumPy version conflicts
   - If issues persist, delete `model_env` folder and run setup again

2. **Out of Memory Error**:
   - Reduce max_length parameter
   - Close other applications
   - Use CPU instead of GPU if necessary

3. **Model Loading Error**:
   - Ensure all model files are present
   - Check internet connection for base model download
   - Verify Python environment has required packages

4. **Slow Generation**:
   - Enable GPU if available
   - Reduce max_length parameter
   - Lower temperature for faster sampling

### Performance Tips:

- **GPU Usage**: The app automatically detects and uses CUDA if available
- **Memory Management**: Model is cached after first load
- **Generation Speed**: Adjust parameters based on your hardware capabilities

## File Structure

```
new_model1/
├── adapter_config.json      # LoRA adapter configuration
├── adapter_model.safetensors # LoRA adapter weights
├── chat_template.jinja      # Chat formatting template
├── tokenizer.json          # Tokenizer vocabulary
├── tokenizer_config.json   # Tokenizer configuration
├── special_tokens_map.json # Special tokens mapping
├── training_args.bin       # Training arguments
├── app.py                  # Main Streamlit application
├── requirements.txt        # Python dependencies
├── run_app.py             # Python setup and run script
├── run_app.bat            # Windows batch file (easiest)
├── .env                   # Hugging Face token (create this)
├── model_env/             # Virtual environment (created automatically)
├── DEPLOYMENT_GUIDE.md    # Deployment instructions
└── README.md              # This file
```

## Technical Details

The application uses:
- **Transformers**: For model loading and tokenization
- **PEFT**: For LoRA adapter integration
- **PyTorch**: For model inference
- **Streamlit**: For the web interface

The model is loaded once and cached for subsequent requests, ensuring fast response times after the initial load.

### Framework versions
- PEFT 0.15.2