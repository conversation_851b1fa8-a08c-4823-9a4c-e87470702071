# 🚀 Deployment Guide - Fine-tuned Llama Model

This guide will help you deploy your fine-tuned Llama model on another computer.

## 📦 Required Files

Copy these files to your target computer:

### Essential Model Files:
```
├── adapter_config.json      # LoRA configuration
├── adapter_model.safetensors # Your fine-tuned weights
├── chat_template.jinja      # Chat formatting
├── tokenizer.json          # Tokenizer vocabulary
├── tokenizer_config.json   # Tokenizer settings
├── special_tokens_map.json # Special tokens
├── training_args.bin       # Training metadata
```

### Application Files:
```
├── app.py                  # Main Streamlit app
├── requirements.txt        # Python dependencies
├── run_app.py             # Setup script
├── run_app.bat            # Windows batch file
├── .env                   # Your HF token (create this)
└── README.md              # Documentation
```

## 🔧 Setup Steps on Target Computer

### Step 1: Prerequisites
- Python 3.8+ installed
- Internet connection (for downloading base model)
- At least 8GB RAM recommended

### Step 2: Copy Files
1. Copy all the files listed above to a folder (e.g., `my_llama_model/`)
2. Navigate to that folder in terminal/command prompt

### Step 3: Create .env File
Create a `.env` file with your Hugging Face token:
```
HF_TOKEN=*************************************
HUGGING_FACE_HUB_TOKEN=*************************************
```

### Step 4: Run the Application

**Windows (Easiest):**
```cmd
run_app.bat
```

**Cross-platform:**
```bash
python run_app.py
```

**Manual Setup:**
```bash
# Create virtual environment
python -m venv model_env

# Activate virtual environment
# Windows:
model_env\Scripts\activate
# Linux/Mac:
source model_env/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run the app
streamlit run app.py --server.port 8501
```

## ⏱️ First Run Expectations

- **Download time**: 5-15 minutes (downloads base Llama model ~2.5GB)
- **Loading time**: 2-5 minutes (loads model into memory)
- **Subsequent runs**: Much faster (model cached locally)

## 🌐 Access the Application

Once running, open your browser to:
- **Local access**: http://localhost:8501
- **Network access**: http://[computer-ip]:8501

## 🔒 Security Notes

- Keep your `.env` file secure (contains your HF token)
- Don't share your Hugging Face token publicly
- The app runs locally - no data is sent to external servers

## 📁 Minimal File Structure

For deployment, you only need these files:
```
my_llama_model/
├── adapter_config.json
├── adapter_model.safetensors
├── chat_template.jinja
├── tokenizer.json
├── tokenizer_config.json
├── special_tokens_map.json
├── training_args.bin
├── app.py
├── requirements.txt
├── .env
└── run_app.bat (Windows) or run_app.py
```

## 🆘 Troubleshooting

### Common Issues:
1. **Import errors**: Use the provided virtual environment setup
2. **Token errors**: Ensure .env file has correct token format
3. **Memory errors**: Close other applications, reduce max_length parameter
4. **Network errors**: Check internet connection for model download

### Getting Help:
- Check the progress indicators in the web interface
- Look at terminal output for detailed error messages
- Ensure all required files are present

## 🎯 Quick Checklist

Before deploying:
- [ ] All model files copied
- [ ] .env file created with valid HF token
- [ ] Python 3.8+ installed on target computer
- [ ] Internet connection available
- [ ] At least 8GB RAM available

After deployment:
- [ ] Virtual environment created successfully
- [ ] Dependencies installed without errors
- [ ] Streamlit app starts without errors
- [ ] Model loads successfully (may take 10-15 minutes first time)
- [ ] Chat interface responds to messages
