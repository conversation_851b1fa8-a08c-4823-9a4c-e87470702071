#!/usr/bin/env python3
"""
Setup and run script for the fine-tuned model Streamlit app
"""

import subprocess
import sys
import os

def setup_virtual_environment():
    """Create and setup virtual environment"""
    venv_path = "model_env"

    if not os.path.exists(venv_path):
        print("Creating virtual environment...")
        try:
            subprocess.check_call([sys.executable, "-m", "venv", venv_path])
            print("✅ Virtual environment created!")
        except subprocess.CalledProcessError as e:
            print(f"❌ Error creating virtual environment: {e}")
            return False
    else:
        print("✅ Virtual environment already exists!")

    return True

def install_requirements():
    """Install required packages in virtual environment"""
    print("Installing required packages in virtual environment...")

    # Use PowerShell to activate venv and install packages
    cmd = 'model_env\\Scripts\\activate.ps1; pip install "numpy<2" torch transformers peft streamlit accelerate safetensors sentencepiece protobuf'

    try:
        result = subprocess.run(["powershell", "-Command", cmd],
                              capture_output=True, text=True, cwd=".")
        if result.returncode == 0:
            print("✅ Requirements installed successfully!")
            return True
        else:
            print(f"❌ Error installing requirements: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error running installation: {e}")
        return False

def run_streamlit():
    """Run the Streamlit app in virtual environment"""
    print("Starting Streamlit app...")

    # Use PowerShell to activate venv and run streamlit
    cmd = 'model_env\\Scripts\\activate.ps1; streamlit run app.py --server.port 8501'

    try:
        subprocess.run(["powershell", "-Command", cmd], cwd=".")
    except KeyboardInterrupt:
        print("\n👋 App stopped by user")
    except Exception as e:
        print(f"❌ Error running app: {e}")

def main():
    print("🤖 Fine-tuned Llama Model Setup")
    print("=" * 40)

    # Check if we're in the right directory
    if not os.path.exists("adapter_config.json"):
        print("❌ Error: adapter_config.json not found!")
        print("Please run this script from the directory containing your fine-tuned model files.")
        return

    print("✅ Model files found!")

    # Setup virtual environment
    if not setup_virtual_environment():
        return

    # Install requirements
    if not install_requirements():
        return

    print("\n🚀 Starting the web interface...")
    print("The app will open in your browser at: http://localhost:8501")
    print("Press Ctrl+C to stop the app")
    print("-" * 40)

    # Run the app
    run_streamlit()

if __name__ == "__main__":
    main()
