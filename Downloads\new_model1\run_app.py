#!/usr/bin/env python3
"""
Setup and run script for the fine-tuned model Streamlit app
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False

def run_streamlit():
    """Run the Streamlit app"""
    print("Starting Streamlit app...")
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py", "--server.port", "8501"])
    except KeyboardInterrupt:
        print("\n👋 App stopped by user")
    except Exception as e:
        print(f"❌ Error running app: {e}")

def main():
    print("🤖 Fine-tuned Llama Model Setup")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not os.path.exists("adapter_config.json"):
        print("❌ Error: adapter_config.json not found!")
        print("Please run this script from the directory containing your fine-tuned model files.")
        return
    
    print("✅ Model files found!")
    
    # Install requirements
    if not install_requirements():
        return
    
    print("\n🚀 Starting the web interface...")
    print("The app will open in your browser at: http://localhost:8501")
    print("Press Ctrl+C to stop the app")
    print("-" * 40)
    
    # Run the app
    run_streamlit()

if __name__ == "__main__":
    main()
