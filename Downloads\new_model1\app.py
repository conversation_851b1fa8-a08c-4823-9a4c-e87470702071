import streamlit as st
import os
import sys
from datetime import datetime

# Load environment variables from .env file if it exists
def load_env_file():
    """Load environment variables from .env file"""
    env_file = ".env"
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value

# Load environment variables
load_env_file()

# Try to import required libraries with error handling
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError as e:
    st.error(f"PyTorch not available: {e}")
    TORCH_AVAILABLE = False

try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    TRANSFORMERS_AVAILABLE = True
except ImportError as e:
    st.error(f"Transformers not available: {e}")
    TRANSFORMERS_AVAILABLE = False

try:
    from peft import PeftModel
    PEFT_AVAILABLE = True
except ImportError as e:
    st.error(f"PEFT not available: {e}")
    PEFT_AVAILABLE = False

# Set page config
st.set_page_config(
    page_title="Fine-tuned Llama Chat",
    page_icon="🤖",
    layout="wide"
)

@st.cache_resource
def load_model():
    """Load the base model and fine-tuned adapter"""
    if not all([TORCH_AVAILABLE, TRANSFORMERS_AVAILABLE, PEFT_AVAILABLE]):
        st.error("Required libraries are not available. Please check the installation.")
        return None, None

    try:
        # Model paths
        base_model_name = "meta-llama/Llama-3.2-1B-Instruct"
        adapter_path = "."  # Current directory contains the adapter

        # Get token from environment
        hf_token = os.environ.get("HF_TOKEN") or os.environ.get("HUGGING_FACE_HUB_TOKEN")

        # Create progress indicators
        progress_bar = st.progress(0)
        status_text = st.empty()

        # Step 1: Load tokenizer
        status_text.text("🔤 Loading tokenizer...")
        progress_bar.progress(10)

        tokenizer = AutoTokenizer.from_pretrained(
            base_model_name,
            token=hf_token,
            trust_remote_code=True
        )
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        progress_bar.progress(25)
        status_text.text("✅ Tokenizer loaded!")

        # Step 2: Load base model (this takes the longest)
        status_text.text("🧠 Downloading and loading base model... (This may take 5-10 minutes on first run)")
        progress_bar.progress(30)

        base_model = AutoModelForCausalLM.from_pretrained(
            base_model_name,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            device_map="auto" if torch.cuda.is_available() else None,
            trust_remote_code=True,
            token=hf_token,
            low_cpu_mem_usage=True  # Optimize memory usage
        )

        progress_bar.progress(70)
        status_text.text("✅ Base model loaded!")

        # Step 3: Load fine-tuned adapter
        status_text.text("🎯 Loading your fine-tuned adapter...")
        progress_bar.progress(80)

        model = PeftModel.from_pretrained(base_model, adapter_path)

        progress_bar.progress(90)
        status_text.text("🔗 Merging adapter weights...")

        model = model.merge_and_unload()  # Merge adapter weights

        progress_bar.progress(95)

        # Step 4: Final setup
        status_text.text("⚙️ Setting up model for inference...")
        model.eval()

        progress_bar.progress(100)
        status_text.text("🎉 Model loaded successfully!")

        # Clear progress indicators after a moment
        import time
        time.sleep(1)
        progress_bar.empty()
        status_text.empty()

        st.success("🎉 Your fine-tuned Llama model is ready!")
        st.info(f"💾 Model size: ~{sum(p.numel() for p in model.parameters()) / 1e6:.1f}M parameters")

        return model, tokenizer

    except Exception as e:
        st.error(f"Error loading model: {str(e)}")
        st.error("Please make sure you have the correct model files and dependencies installed.")
        return None, None

def format_chat_prompt(messages, tokenizer):
    """Format messages using the chat template"""
    try:
        # Use the tokenizer's chat template
        formatted_prompt = tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )
        return formatted_prompt
    except Exception as e:
        st.error(f"Error formatting prompt: {str(e)}")
        return None

def generate_response(model, tokenizer, prompt, max_length=512, temperature=0.7, top_p=0.9):
    """Generate response from the model"""
    try:
        # Tokenize input
        inputs = tokenizer.encode(prompt, return_tensors="pt")
        
        # Move to same device as model
        device = next(model.parameters()).device
        inputs = inputs.to(device)
        
        # Generate response
        with torch.no_grad():
            outputs = model.generate(
                inputs,
                max_length=len(inputs[0]) + max_length,
                temperature=temperature,
                top_p=top_p,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
                repetition_penalty=1.1
            )
        
        # Decode response
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Extract only the new generated part
        response = response[len(tokenizer.decode(inputs[0], skip_special_tokens=True)):].strip()
        
        return response
        
    except Exception as e:
        st.error(f"Error generating response: {str(e)}")
        return None

def main():
    st.title("🤖 Fine-tuned Llama Chat Interface")
    st.markdown("Chat with your fine-tuned Llama-3.2-1B-Instruct model")

    # Sidebar for Hugging Face token
    st.sidebar.header("🔑 Authentication")

    # Check if token is already available
    existing_token = os.environ.get("HF_TOKEN") or os.environ.get("HUGGING_FACE_HUB_TOKEN")

    if existing_token:
        st.sidebar.success("✅ Token loaded from environment")
        st.sidebar.info("If you need to change the token, enter a new one below:")
    else:
        st.sidebar.warning("⚠️ No token found. Please enter your Hugging Face token.")

    hf_token = st.sidebar.text_input(
        "Hugging Face Token",
        type="password",
        help="Enter your Hugging Face access token to download the base model",
        placeholder="hf_..." if not existing_token else "Enter new token to override"
    )

    if hf_token:
        # Set the token as environment variable
        os.environ["HF_TOKEN"] = hf_token
        # Also try the alternative environment variable name
        os.environ["HUGGING_FACE_HUB_TOKEN"] = hf_token
        st.sidebar.success("✅ Token updated!")

    # Load model
    model, tokenizer = load_model()
    
    if model is None or tokenizer is None:
        st.error("Failed to load model. Please check your model files.")
        return
    
    # Sidebar for settings
    st.sidebar.header("Generation Settings")
    max_length = st.sidebar.slider("Max Response Length", 50, 1000, 512)
    temperature = st.sidebar.slider("Temperature", 0.1, 2.0, 0.7, 0.1)
    top_p = st.sidebar.slider("Top-p", 0.1, 1.0, 0.9, 0.05)
    
    # Initialize chat history
    if "messages" not in st.session_state:
        st.session_state.messages = []
    
    # Display chat history
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
    
    # Chat input
    if prompt := st.chat_input("What would you like to ask?"):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Generate assistant response
        with st.chat_message("assistant"):
            with st.spinner("Thinking..."):
                # Format the conversation for the model
                formatted_prompt = format_chat_prompt(st.session_state.messages, tokenizer)
                
                if formatted_prompt:
                    # Generate response
                    response = generate_response(
                        model, tokenizer, formatted_prompt, 
                        max_length, temperature, top_p
                    )
                    
                    if response:
                        st.markdown(response)
                        # Add assistant response to chat history
                        st.session_state.messages.append({"role": "assistant", "content": response})
                    else:
                        st.error("Failed to generate response")
                else:
                    st.error("Failed to format prompt")
    
    # Clear chat button
    if st.sidebar.button("Clear Chat"):
        st.session_state.messages = []
        st.rerun()
    
    # Model info
    st.sidebar.markdown("---")
    st.sidebar.markdown("### Model Info")
    st.sidebar.markdown("**Base Model:** meta-llama/Llama-3.2-1B-Instruct")
    st.sidebar.markdown("**Fine-tuning:** LoRA Adapter")
    st.sidebar.markdown(f"**Device:** {'CUDA' if torch.cuda.is_available() else 'CPU'}")

if __name__ == "__main__":
    main()
